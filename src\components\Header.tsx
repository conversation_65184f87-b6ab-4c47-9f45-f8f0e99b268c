
import { Menu, X } from "lucide-react";
import { useState } from "react";

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="fixed top-0 w-full z-50 bg-white/80 backdrop-blur-lg border-b border-gray-100 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center gap-3 group cursor-pointer">
            <div className="relative">
              <div className="absolute inset-0 bg-lia-green/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300"></div>
              <img
                src="/lia-logo.png"
                alt="Lia Logo"
                className="relative w-10 h-10 object-contain group-hover:scale-110 transition-transform duration-300"
              />
            </div>
            <span className="text-xl font-black tracking-tight text-gray-900 group-hover:text-lia-green transition-colors duration-300">
              LIA
            </span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {[
              { href: "#como-funciona", label: "Como Funciona" },
              { href: "#vantagens", label: "Vantagens" },
              { href: "#planos", label: "Planos" },
              { href: "#depoimentos", label: "Depoimentos" },
              { href: "#faq", label: "FAQ" }
            ].map((item) => (
              <a
                key={item.href}
                href={item.href}
                className="relative text-gray-600 hover:text-lia-green font-medium transition-all duration-300 group"
              >
                {item.label}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-lia-green group-hover:w-full transition-all duration-300"></span>
              </a>
            ))}
          </nav>

          {/* CTA Button */}
          <div className="hidden md:block">
            <a
              href="#planos"
              className="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-lia-green to-emerald-500 text-white font-semibold rounded-full hover:shadow-lg hover:shadow-lia-green/25 transform hover:-translate-y-0.5 transition-all duration-300"
            >
              Começar Agora
            </a>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden p-2 rounded-lg text-gray-600 hover:text-lia-green hover:bg-gray-50 transition-colors duration-300"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-100 bg-white/95 backdrop-blur-sm">
            <nav className="flex flex-col space-y-3">
              {[
                { href: "#como-funciona", label: "Como Funciona" },
                { href: "#vantagens", label: "Vantagens" },
                { href: "#planos", label: "Planos" },
                { href: "#depoimentos", label: "Depoimentos" },
                { href: "#faq", label: "FAQ" }
              ].map((item) => (
                <a
                  key={item.href}
                  href={item.href}
                  className="block px-4 py-2 text-gray-600 hover:text-lia-green hover:bg-gray-50 rounded-lg font-medium transition-colors duration-300"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.label}
                </a>
              ))}
              <div className="px-4 pt-2">
                <a
                  href="#planos"
                  className="block w-full text-center px-6 py-3 bg-gradient-to-r from-lia-green to-emerald-500 text-white font-semibold rounded-full"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Começar Agora
                </a>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
