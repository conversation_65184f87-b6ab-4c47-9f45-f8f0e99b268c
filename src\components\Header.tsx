
import { Menu, X } from "lucide-react";
import { useState } from "react";

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="fixed top-0 w-full z-50 bg-white/95 backdrop-blur-2xl border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center gap-3 group cursor-pointer">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-lia-green to-emerald-600 rounded-xl flex items-center justify-center shadow-sm group-hover:shadow-md transition-all duration-200 p-1.5">
                <img
                  src="/lia-logo.png"
                  alt="Lia Logo"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <span className="text-xl font-bold tracking-tight text-gray-900">
              LIA
            </span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {[
              { href: "#como-funciona", label: "Como Funciona" },
              { href: "#vantagens", label: "Vantagens" },
              { href: "#planos", label: "Planos" },
              { href: "#depoimentos", label: "Depoimentos" },
              { href: "#faq", label: "FAQ" }
            ].map((item) => (
              <a
                key={item.href}
                href={item.href}
                className="text-gray-600 hover:text-lia-green font-medium text-sm transition-colors duration-200"
              >
                {item.label}
              </a>
            ))}
          </nav>

          {/* CTA Button */}
          <div className="hidden md:block">
            <a
              href="#planos"
              className="inline-flex items-center px-6 py-2.5 bg-lia-green text-white font-semibold text-sm rounded-lg hover:bg-lia-green/90 transition-colors duration-200"
            >
              Começar Agora
            </a>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden p-2 rounded-lg text-gray-600 hover:text-lia-green hover:bg-gray-50 transition-colors duration-200"
          >
            {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-100 bg-white">
            <nav className="flex flex-col space-y-1">
              {[
                { href: "#como-funciona", label: "Como Funciona" },
                { href: "#vantagens", label: "Vantagens" },
                { href: "#planos", label: "Planos" },
                { href: "#depoimentos", label: "Depoimentos" },
                { href: "#faq", label: "FAQ" }
              ].map((item) => (
                <a
                  key={item.href}
                  href={item.href}
                  className="block px-6 py-3 text-gray-600 hover:text-lia-green font-medium transition-colors duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.label}
                </a>
              ))}
              <div className="px-6 pt-4">
                <a
                  href="#planos"
                  className="block w-full text-center px-6 py-3 bg-lia-green text-white font-semibold rounded-lg hover:bg-lia-green/90 transition-colors duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Começar Agora
                </a>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
