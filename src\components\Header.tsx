
import { Menu, X } from "lucide-react";
import { useState } from "react";

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="fixed top-0 w-full z-50 bg-white/90 backdrop-blur-xl border-b border-gray-200/50 shadow-lg shadow-gray-900/5">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex items-center gap-3 group cursor-pointer">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-lia-green to-emerald-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 p-1">
                <img
                  src="/lia-logo.png"
                  alt="Lia Logo"
                  className="w-full h-full object-contain group-hover:scale-110 transition-transform duration-300"
                />
              </div>
            </div>
            <span className="text-xl font-black tracking-tight text-gray-900 group-hover:text-lia-green transition-colors duration-300">
              LIA
            </span>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-10">
            {[
              { href: "#como-funciona", label: "Como Funciona" },
              { href: "#vantagens", label: "Vantagens" },
              { href: "#planos", label: "Planos" },
              { href: "#depoimentos", label: "Depoimentos" },
              { href: "#faq", label: "FAQ" }
            ].map((item) => (
              <a
                key={item.href}
                href={item.href}
                className="relative text-gray-700 hover:text-lia-green font-semibold text-sm transition-all duration-300 group py-2 px-3 rounded-lg hover:bg-lia-green/5"
              >
                {item.label}
                <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-lia-green to-emerald-500 group-hover:w-3/4 transition-all duration-300"></span>
              </a>
            ))}
          </nav>

          {/* CTA Button */}
          <div className="hidden md:block">
            <a
              href="#planos"
              className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-lia-green to-emerald-500 text-white font-bold text-sm rounded-2xl hover:shadow-xl hover:shadow-lia-green/30 transform hover:-translate-y-1 hover:scale-105 transition-all duration-300 border border-lia-green/20"
            >
              ✨ Começar Agora
            </a>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden p-3 rounded-xl text-gray-700 hover:text-lia-green hover:bg-lia-green/10 transition-all duration-300 border border-gray-200 hover:border-lia-green/30"
          >
            {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-6 border-t border-gray-200/50 bg-white/95 backdrop-blur-xl">
            <nav className="flex flex-col space-y-2">
              {[
                { href: "#como-funciona", label: "Como Funciona" },
                { href: "#vantagens", label: "Vantagens" },
                { href: "#planos", label: "Planos" },
                { href: "#depoimentos", label: "Depoimentos" },
                { href: "#faq", label: "FAQ" }
              ].map((item) => (
                <a
                  key={item.href}
                  href={item.href}
                  className="block px-4 py-3 text-gray-700 hover:text-lia-green hover:bg-lia-green/10 rounded-xl font-semibold transition-all duration-300 mx-2"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.label}
                </a>
              ))}
              <div className="px-6 pt-4">
                <a
                  href="#planos"
                  className="block w-full text-center px-6 py-4 bg-gradient-to-r from-lia-green to-emerald-500 text-white font-bold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  ✨ Começar Agora
                </a>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
