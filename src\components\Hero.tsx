
import { Message<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, <PERSON>, Zap } from "lucide-react";

const Hero = () => (
  <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-emerald-50/30 overflow-hidden">
    {/* Background Elements */}
    <div className="absolute inset-0">
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-lia-green/5 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-emerald-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-lia-green/5 to-emerald-300/5 rounded-full blur-3xl"></div>
    </div>

    <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-28 pb-16">
      <div className="text-center space-y-8">
        {/* Badge */}
        <div className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-white/90 to-lia-green/5 backdrop-blur-xl border border-lia-green/30 rounded-2xl shadow-xl shadow-lia-green/10">
          <Sparkles className="w-5 h-5 text-lia-green" />
          <span className="text-sm font-bold text-gray-800">Inteligência Artificial Financeira</span>
          <div className="w-2 h-2 bg-lia-green rounded-full animate-pulse"></div>
        </div>

        {/* Main Headline */}
        <div className="space-y-6">
          <h1 className="text-5xl sm:text-6xl lg:text-8xl font-black text-gray-900 leading-[0.9] tracking-tight">
            Sua vida financeira
            <span className="block bg-gradient-to-r from-lia-green via-emerald-500 to-lia-green bg-clip-text text-transparent animate-gradient">
              organizada e simples
            </span>
            <span className="block text-gray-900">direto no WhatsApp</span>
          </h1>
        </div>

        {/* Subheadline */}
        <p className="text-xl sm:text-3xl text-gray-600 max-w-5xl mx-auto leading-relaxed font-medium">
          Pare de se preocupar com bagunça, atrasos e falta de clareza.
          <br />
          <span className="text-lia-green font-bold">A Lia deixa o controle do seu dinheiro leve, simples e automático</span>.
          <br />
          <span className="text-gray-800 font-semibold">Sinta agora o alívio e tranquilidade de ter tudo resolvido.</span>
        </p>

        {/* Trust Indicators */}
        <div className="flex flex-wrap justify-center items-center gap-8 text-sm">
          <div className="flex items-center gap-3 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200/50 shadow-sm">
            <Shield className="w-5 h-5 text-lia-green" />
            <span className="font-semibold text-gray-700">100% Seguro</span>
          </div>
          <div className="flex items-center gap-3 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200/50 shadow-sm">
            <Zap className="w-5 h-5 text-lia-green" />
            <span className="font-semibold text-gray-700">Resposta Instantânea</span>
          </div>
          <div className="flex items-center gap-3 px-4 py-2 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200/50 shadow-sm">
            <MessageCircle className="w-5 h-5 text-lia-green" />
            <span className="font-semibold text-gray-700">Direto no WhatsApp</span>
          </div>
        </div>

        {/* CTA Section */}
        <div className="space-y-8">
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <a
              href="#planos"
              className="group inline-flex items-center justify-center gap-4 px-12 py-5 bg-gradient-to-r from-lia-green to-emerald-500 text-white font-black text-xl rounded-3xl shadow-2xl hover:shadow-2xl hover:shadow-lia-green/40 transform hover:-translate-y-2 hover:scale-105 transition-all duration-300 border border-lia-green/20"
            >
              🚀 Quero organizar minha vida financeira!
              <ArrowRight className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300" />
            </a>
          </div>

          {/* Live Status */}
          <div className="inline-flex items-center gap-4 px-6 py-3 bg-white/70 backdrop-blur-sm rounded-2xl border border-gray-200/50 shadow-lg">
            <div className="relative">
              <div className="w-3 h-3 bg-lia-green rounded-full"></div>
              <div className="absolute inset-0 w-3 h-3 bg-lia-green rounded-full animate-ping opacity-75"></div>
            </div>
            <span className="font-semibold text-gray-700 text-sm">
              Online agora • Comece em segundos • Experimente a paz financeira
            </span>
          </div>
        </div>

        {/* Social Proof Preview */}
        <div className="pt-16">
          <div className="inline-flex items-center gap-6 px-8 py-4 bg-gradient-to-r from-white/80 to-lia-green/5 backdrop-blur-xl rounded-3xl border border-lia-green/20 shadow-xl shadow-lia-green/10">
            <div className="flex -space-x-3">
              {[
                "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face&auto=format",
                "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face&auto=format",
                "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face&auto=format"
              ].map((src, i) => (
                <img
                  key={i}
                  src={src}
                  alt={`Usuário ${i + 1}`}
                  className="w-10 h-10 rounded-full border-3 border-white object-cover shadow-lg"
                />
              ))}
            </div>
            <div className="text-left">
              <div className="text-lg font-black text-gray-900">
                +500 pessoas já organizaram suas finanças
              </div>
              <div className="text-sm text-gray-600 font-medium">
                Junte-se a elas e transforme sua vida financeira
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Floating Elements */}
    <div className="absolute top-20 left-10 w-20 h-20 bg-lia-green/10 rounded-2xl rotate-12 animate-bounce delay-300"></div>
    <div className="absolute bottom-20 right-10 w-16 h-16 bg-emerald-300/10 rounded-full animate-bounce delay-700"></div>
  </section>
);

export default Hero;
