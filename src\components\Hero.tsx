
import { Message<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spark<PERSON>, <PERSON>, Zap } from "lucide-react";

const Hero = () => (
  <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-emerald-50/30 overflow-hidden">
    {/* Background Elements */}
    <div className="absolute inset-0">
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-lia-green/5 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-emerald-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-lia-green/5 to-emerald-300/5 rounded-full blur-3xl"></div>
    </div>

    <div className="relative z-10 max-w-5xl mx-auto px-6 lg:px-8 pt-24 pb-16">
      <div className="text-center space-y-8">
        {/* Badge */}
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-lia-green/10 border border-lia-green/20 rounded-full">
          <div className="w-2 h-2 bg-lia-green rounded-full"></div>
          <span className="text-sm font-medium text-lia-green">Inteligência Artificial Financeira</span>
        </div>

        {/* Main Headline */}
        <div className="space-y-6">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight tracking-tight">
            Sua vida financeira
            <span className="block text-lia-green">
              organizada e simples
            </span>
            <span className="block text-gray-900">direto no WhatsApp</span>
          </h1>
        </div>

        {/* Subheadline */}
        <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          Transforme sua relação com o dinheiro através de uma assistente inteligente que organiza suas finanças automaticamente via WhatsApp.
        </p>

        {/* Trust Indicators */}
        <div className="flex flex-wrap justify-center items-center gap-6 text-sm">
          <div className="flex items-center gap-2">
            <Shield className="w-4 h-4 text-lia-green" />
            <span className="text-gray-600">100% Seguro</span>
          </div>
          <div className="flex items-center gap-2">
            <Zap className="w-4 h-4 text-lia-green" />
            <span className="text-gray-600">Resposta Instantânea</span>
          </div>
          <div className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4 text-lia-green" />
            <span className="text-gray-600">Direto no WhatsApp</span>
          </div>
        </div>

        {/* CTA Section */}
        <div className="space-y-6">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#planos"
              className="group inline-flex items-center justify-center gap-2 px-8 py-4 bg-lia-green text-white font-semibold text-lg rounded-lg hover:bg-lia-green/90 transition-colors duration-200"
            >
              Começar Agora
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </a>
          </div>

          {/* Live Status */}
          <div className="inline-flex items-center gap-3 px-4 py-2 bg-white border border-gray-200 rounded-full">
            <div className="w-2 h-2 bg-lia-green rounded-full"></div>
            <span className="text-sm text-gray-600">
              Online agora • Comece em segundos
            </span>
          </div>
        </div>

        {/* Social Proof Preview */}
        <div className="pt-12">
          <div className="inline-flex items-center gap-4 px-6 py-3 bg-white border border-gray-200 rounded-full">
            <div className="flex -space-x-2">
              {[
                "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face&auto=format",
                "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100&h=100&fit=crop&crop=face&auto=format",
                "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face&auto=format"
              ].map((src, i) => (
                <img
                  key={i}
                  src={src}
                  alt={`Usuário ${i + 1}`}
                  className="w-8 h-8 rounded-full border-2 border-white object-cover"
                />
              ))}
            </div>
            <span className="text-sm font-medium text-gray-700">
              +500 pessoas já organizaram suas finanças
            </span>
          </div>
        </div>
      </div>
    </div>

    {/* Floating Elements */}
    <div className="absolute top-20 left-10 w-20 h-20 bg-lia-green/10 rounded-2xl rotate-12 animate-bounce delay-300"></div>
    <div className="absolute bottom-20 right-10 w-16 h-16 bg-emerald-300/10 rounded-full animate-bounce delay-700"></div>
  </section>
);

export default Hero;
