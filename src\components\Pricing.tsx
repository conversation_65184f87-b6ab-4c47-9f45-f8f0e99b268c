
import { DollarSign, Check, Star, Zap } from "lucide-react";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const plans = [
  {
    title: "Mensal",
    price: "R$39,90",
    period: "por mês",
    highlight: false,
    popular: false,
    savings: null,
    features: [
      "Controle financeiro completo",
      "Lembretes automáticos de contas",
      "Suporte 24/7 no WhatsApp",
      "Relatórios mensais detalhados",
      "Categorização automática"
    ]
  },
  {
    title: "Anual",
    price: "R$299,90",
    period: "por ano",
    highlight: true,
    popular: true,
    savings: "Economize 2 meses",
    features: [
      "Tudo do plano mensal",
      "2 meses grátis",
      "Insights avançados com IA",
      "Suporte prioritário",
      "Metas personalizadas",
      "Análise de padrões de gastos"
    ]
  },
];

const Pricing = () => (
  <section className="py-24 bg-gradient-to-br from-gray-50 to-emerald-50/30" id="planos">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center max-w-4xl mx-auto mb-20">
        <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-lia-green/10 to-emerald-500/10 rounded-full mb-8 border border-lia-green/20">
          <DollarSign className="w-5 h-5 text-lia-green" />
          <span className="text-sm font-bold text-lia-green">PLANOS E PREÇOS</span>
        </div>
        <h2 className="text-4xl sm:text-6xl font-black text-gray-900 mb-8 leading-tight">
          Invista pouco para conquistar
          <span className="block bg-gradient-to-r from-lia-green to-emerald-500 bg-clip-text text-transparent">
            sua liberdade financeira
          </span>
        </h2>
        <p className="text-xl sm:text-2xl text-gray-600 leading-relaxed mb-10 max-w-3xl mx-auto">
          Controle e tranquilidade para o seu dinheiro na palma da mão.
          <span className="font-semibold text-lia-green"> Escolha o plano e mude sua história financeira hoje mesmo.</span>
        </p>
        <div className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-lia-green/5 to-emerald-500/5 rounded-2xl border border-lia-green/20 shadow-lg">
          <Zap className="w-5 h-5 text-lia-green" />
          <span className="text-base font-semibold text-gray-700">
            Cancele quando quiser • Sem taxas ocultas • Suporte 24/7
          </span>
        </div>
      </div>

      {/* Plans */}
      <div className="flex flex-col lg:flex-row gap-10 justify-center items-center lg:items-stretch max-w-5xl mx-auto">
        {plans.map((plan) => (
          <div key={plan.title} className="relative w-full max-w-md">
            {/* Popular Badge */}
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-lia-green to-emerald-500 text-white text-xs font-bold rounded-full shadow-lg">
                  <Star className="w-3 h-3" />
                  MAIS POPULAR
                </div>
              </div>
            )}

            <Card className={`relative h-full overflow-hidden ${
              plan.highlight
                ? "border-2 border-lia-green shadow-2xl shadow-lia-green/25 scale-105 bg-gradient-to-br from-white to-lia-green/5"
                : "border border-gray-200 shadow-xl bg-white"
            } transition-all duration-500 hover:shadow-2xl hover:-translate-y-3 rounded-3xl`}>
              <CardHeader className="relative text-center pb-6 pt-8">
                <div className={`inline-flex items-center justify-center w-20 h-20 ${
                  plan.highlight
                    ? "bg-gradient-to-br from-lia-green to-emerald-500 shadow-xl shadow-lia-green/30"
                    : "bg-gradient-to-br from-gray-100 to-gray-200 shadow-lg"
                } rounded-3xl mx-auto mb-6`}>
                  <DollarSign className={`w-10 h-10 ${plan.highlight ? "text-white" : "text-gray-600"}`} />
                </div>

                <CardTitle className="text-3xl font-black text-gray-900 mb-4">
                  {plan.title}
                </CardTitle>

                {plan.savings && (
                  <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-lia-green/10 to-emerald-500/10 text-lia-green text-sm font-bold rounded-full border border-lia-green/20">
                    <Star className="w-4 h-4" />
                    {plan.savings}
                  </div>
                )}
              </CardHeader>

              <CardContent className="relative text-center pb-8 px-8">
                {/* Price */}
                <div className="mb-10">
                  <div className="flex items-baseline justify-center gap-2 mb-2">
                    <span className="text-5xl font-black bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                      {plan.price}
                    </span>
                    <span className="text-gray-500 font-semibold text-lg">
                      {plan.period}
                    </span>
                  </div>
                  {plan.title === "Anual" && (
                    <div className="text-sm text-lia-green font-semibold mt-2 bg-lia-green/10 px-3 py-1 rounded-full inline-block">
                      Equivale a R$ 33,32/mês
                    </div>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-5 text-left">
                  {plan.features.map((feature, idx) => (
                    <div key={idx} className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-br from-lia-green to-emerald-500 rounded-full flex items-center justify-center shadow-sm">
                        <Check className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-gray-700 font-medium leading-relaxed">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>

              <CardFooter className="relative pt-0 px-8 pb-8">
                <Button
                  className={`w-full ${
                    plan.highlight
                      ? "bg-gradient-to-r from-lia-green to-emerald-500 hover:from-emerald-600 hover:to-lia-green text-white shadow-xl hover:shadow-2xl hover:shadow-lia-green/40"
                      : "bg-white border-2 border-lia-green text-lia-green hover:bg-lia-green hover:text-white shadow-lg hover:shadow-xl"
                  } font-bold text-lg py-7 rounded-2xl transition-all duration-300 transform hover:-translate-y-2 hover:scale-105`}
                  size="lg"
                >
                  {plan.highlight ? "🚀 Escolher Plano Premium" : "✨ Começar Agora"}
                </Button>
              </CardFooter>
            </Card>
          </div>
        ))}
      </div>

      {/* Bottom Message */}
      <div className="text-center mt-20">
        <div className="bg-gradient-to-r from-lia-green to-emerald-500 rounded-3xl p-8 lg:p-12 text-white shadow-2xl">
          <div className="max-w-3xl mx-auto">
            <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-2xl lg:text-3xl font-black mb-4">
              Chegou a sua vez de viver em paz com seu dinheiro
            </h3>
            <p className="text-white/90 text-lg mb-8 leading-relaxed">
              Experimente a Lia e transforme sua vida financeira hoje mesmo!
              <span className="font-bold"> Cancele quando quiser, sem burocracia.</span>
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-sm font-semibold">
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4" />
                <span>Sem taxas ocultas</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4" />
                <span>Suporte 24/7</span>
              </div>
              <div className="flex items-center gap-2">
                <Check className="w-4 h-4" />
                <span>Cancele quando quiser</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default Pricing;
