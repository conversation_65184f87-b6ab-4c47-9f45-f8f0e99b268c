
import { Smartphone, Zap, Target, ArrowRight } from "lucide-react";

const steps = [
  {
    icon: <Smartphone className="w-8 h-8" />,
    title: "Chame a Lia no WhatsApp",
    desc: "Sem baixar apps ou criar cadastros complicados. Basta mandar um 'oi' e começar a organizar sua vida financeira agora mesmo.",
    color: "from-blue-500 to-blue-600"
  },
  {
    icon: <Zap className="w-8 h-8" />,
    title: "Compartilhe seus gastos e contas",
    desc: "A Lia entende e registra automaticamente tudo, trazendo clareza de onde vai seu dinheiro.",
    color: "from-lia-green to-emerald-500"
  },
  {
    icon: <Target className="w-8 h-8" />,
    title: "Receba insights e lembretes práticos",
    desc: "Metas, alertas de contas e dicas sem te incomodar. Tenha uma rotina mais leve, sob controle e tranquila.",
    color: "from-purple-500 to-purple-600"
  },
];

const HowItWorks = () => (
  <section id="como-funciona" className="py-24 bg-white">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center max-w-4xl mx-auto mb-20">
        <div className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-lia-green/10 to-emerald-500/10 rounded-2xl mb-8 border border-lia-green/20">
          <Zap className="w-5 h-5 text-lia-green" />
          <span className="text-sm font-bold text-lia-green">COMO FUNCIONA</span>
        </div>
        <h2 className="text-4xl sm:text-6xl font-black text-gray-900 mb-8 leading-tight">
          Como a Lia transforma
          <span className="block bg-gradient-to-r from-lia-green to-emerald-500 bg-clip-text text-transparent">
            sua rotina financeira
          </span>
        </h2>
        <p className="text-xl sm:text-2xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
          Em apenas <span className="font-bold text-lia-green">3 passos simples</span>, você terá o controle total das suas finanças
        </p>
      </div>

      {/* Steps */}
      <div className="relative">
        {/* Connection Lines */}
        <div className="hidden lg:block absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-0.5 bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-10 lg:gap-16">
          {steps.map((step, idx) => (
            <div key={step.title} className="relative group">
              {/* Step Number */}
              <div className="absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-r from-lia-green to-emerald-500 text-white rounded-2xl flex items-center justify-center text-lg font-black shadow-xl z-10 border-4 border-white">
                {idx + 1}
              </div>

              {/* Card */}
              <div className="relative bg-gradient-to-br from-white to-gray-50/50 rounded-3xl p-10 shadow-xl border border-gray-200/50 hover:shadow-2xl hover:-translate-y-3 transition-all duration-500 group-hover:border-lia-green/30 backdrop-blur-sm">
                {/* Icon */}
                <div className={`inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br ${step.color} text-white rounded-3xl mb-8 shadow-xl group-hover:scale-110 transition-transform duration-300`}>
                  {step.icon}
                </div>

                {/* Content */}
                <h3 className="text-2xl font-black text-gray-900 mb-6 group-hover:text-lia-green transition-colors duration-300 leading-tight">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed text-lg font-medium">
                  {step.desc}
                </p>

                {/* Arrow for desktop */}
                {idx < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-8 transform -translate-y-1/2">
                    <ArrowRight className="w-8 h-8 text-gray-300 group-hover:text-lia-green transition-colors duration-300" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Bottom CTA */}
      <div className="text-center mt-20">
        <a
          href="#planos"
          className="inline-flex items-center gap-4 px-12 py-5 bg-gradient-to-r from-lia-green to-emerald-500 text-white font-black text-xl rounded-3xl shadow-2xl hover:shadow-2xl hover:shadow-lia-green/40 hover:-translate-y-2 hover:scale-105 transition-all duration-300 border border-lia-green/20"
        >
          🚀 Começar minha transformação financeira
          <ArrowRight className="w-6 h-6" />
        </a>
      </div>
    </div>
  </section>
);

export default HowItWorks;
