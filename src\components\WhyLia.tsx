
import { Use<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Heart, Trophy } from "lucide-react";

const benefits = [
  {
    icon: <Zap className="w-6 h-6" />,
    title: "Tudo pelo WhatsApp",
    desc: "Sem apps extras ou planilhas complicadas",
    color: "from-blue-500 to-blue-600"
  },
  {
    icon: <Shield className="w-6 h-6" />,
    title: "Privacidade total",
    desc: "Seus dados protegidos, só você tem acesso",
    color: "from-green-500 to-emerald-600"
  },
  {
    icon: <Clock className="w-6 h-6" />,
    title: "Alertas automáticos",
    desc: "Dicas para organizar e manter seu dinheiro sob controle",
    color: "from-purple-500 to-purple-600"
  },
  {
    icon: <Trophy className="w-6 h-6" />,
    title: "Liberdade total",
    desc: "Cancele quando quiser, sem burocracia",
    color: "from-orange-500 to-red-500"
  },
  {
    icon: <Heart className="w-6 h-6" />,
    title: "Mais tranquilidade",
    desc: "Todos os dias com paz financeira",
    color: "from-pink-500 to-rose-500"
  }
];

const WhyLia = () => (
  <section id="vantagens" className="py-24 bg-white">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center max-w-4xl mx-auto mb-16">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-lia-green/10 border border-lia-green/20 rounded-full mb-6">
          <span className="text-sm font-medium text-lia-green">VANTAGENS</span>
        </div>
        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
          Por que escolher
          <span className="block text-lia-green">
            a Lia?
          </span>
        </h2>
        <p className="text-lg text-gray-600 leading-relaxed max-w-2xl mx-auto">
          Desenvolvida pensando na sua experiência e segurança
        </p>
      </div>

      {/* Benefits Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {benefits.map((benefit, idx) => (
          <div key={idx} className="group relative">
            {/* Card */}
            <div className="bg-white rounded-2xl p-8 border border-gray-200 hover:border-lia-green/30 hover:shadow-lg transition-all duration-300 h-full">
              {/* Icon */}
              <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${benefit.color} text-white rounded-xl mb-6`}>
                <div className="w-8 h-8 flex items-center justify-center">
                  {benefit.icon}
                </div>
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-900 mb-4 leading-tight">
                {benefit.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {benefit.desc}
              </p>
            </div>
          </div>
        ))}

        {/* Special Featured Card */}
        <div className="md:col-span-2 lg:col-span-1">
          <div className="bg-lia-green rounded-2xl p-8 text-white hover:shadow-lg transition-all duration-300 h-full">
            <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mb-6">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-bold mb-4 leading-tight">
              Resultados Imediatos
            </h3>
            <p className="text-white/90 leading-relaxed">
              Veja a diferença na sua organização financeira desde a primeira conversa com a Lia
            </p>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="mt-20 bg-gradient-to-r from-gray-50 to-emerald-50/30 rounded-3xl p-8 lg:p-12">
        <div className="text-center mb-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Números que comprovam nossa eficiência
          </h3>
        </div>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {[
            { number: "500+", label: "Usuários ativos" },
            { number: "98%", label: "Satisfação" },
            { number: "24/7", label: "Disponibilidade" },
            { number: "2seg", label: "Tempo resposta" }
          ].map((stat, idx) => (
            <div key={idx} className="text-center">
              <div className="text-3xl lg:text-4xl font-black text-lia-green mb-2">
                {stat.number}
              </div>
              <div className="text-gray-600 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </section>
);

export default WhyLia;
