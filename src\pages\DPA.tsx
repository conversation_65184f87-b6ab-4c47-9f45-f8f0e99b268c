
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const DPA = () => (
  <div className="min-h-screen bg-white">
    <Header />
    <main className="pt-28 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="prose prose-lg max-w-none">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-black text-gray-900 mb-4">📄 CONTRATO DE PROCESSAMENTO DE DADOS (DPA)</h1>
            <h2 className="text-2xl font-bold text-lia-green mb-2">LIA – ASSISTENTE FINANCEIRA</h2>
          </div>

          <div className="bg-gradient-to-r from-lia-green/10 to-emerald-500/10 p-6 rounded-2xl mb-8 border border-lia-green/20">
            <div className="space-y-2 text-gray-700">
              <p><strong>Entre:</strong> <PERSON><PERSON> (Controlador)</p>
              <p><strong>E:</strong> [Nome da ferramenta ou serviço] (Operador de Dados)</p>
              <p><strong>Objeto:</strong> Regular o tratamento de dados pessoais realizado pelo Operador em nome do Controlador.</p>
            </div>
          </div>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">1. Obrigações do Operador</h2>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li>Tratar dados somente conforme instruções do Controlador</li>
              <li>Adotar medidas de segurança compatíveis</li>
              <li>Notificar incidentes de segurança imediatamente</li>
              <li>Permitir auditoria quando requisitado</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">2. Tipos de Dados</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Dados pessoais e sensíveis (financeiros e multimídia)
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">3. Finalidade</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Prestação de serviços de processamento, armazenamento, integração ou análise de dados.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">4. Subcontratação</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Permitida apenas com autorização prévia do Controlador.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">5. Vigência</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Enquanto houver relação de tratamento de dados.
            </p>
          </section>

          <div className="bg-gray-50 p-6 rounded-2xl mt-12">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Contato</h3>
            <div className="space-y-2 text-gray-700">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>WhatsApp:</strong> +55 17 3198-1086</p>
              <p><strong>Localidade:</strong> São José do Rio Preto – SP</p>
            </div>
          </div>
        </div>
      </div>
    </main>
    <Footer />
  </div>
);

export default DPA;
