
import { MessageCircle, Mail, MapPin, Phone } from "lucide-react";

const Footer = () => (
  <footer className="bg-gray-900 text-white">
    <div className="max-w-7xl mx-auto px-6 lg:px-8">
      {/* Main Footer */}
      <div className="py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-lia-green rounded-xl flex items-center justify-center p-1.5">
                <img
                  src="/lia-logo.png"
                  alt="Lia Logo"
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="text-2xl font-bold">LIA</span>
            </div>
            <p className="text-gray-400 leading-relaxed">
              Sua assistente financeira inteligente que transforma a forma como você controla seu dinheiro. Simples, seguro e eficiente.
            </p>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-lia-green rounded-full"></div>
              <span className="text-sm text-gray-400">Online 24/7</span>
            </div>
          </div>

          {/* Links Rápidos */}
          <div>
            <h3 className="text-lg font-bold mb-6 text-white">Links Rápidos</h3>
            <ul className="space-y-3">
              {[
                { href: "#como-funciona", label: "Como Funciona" },
                { href: "#vantagens", label: "Vantagens" },
                { href: "#planos", label: "Planos" },
                { href: "#depoimentos", label: "Depoimentos" },
                { href: "#faq", label: "FAQ" }
              ].map((link) => (
                <li key={link.href}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-lia-green transition-colors duration-200"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Documentos Legais */}
          <div>
            <h3 className="text-lg font-bold mb-6 text-white">Documentos</h3>
            <ul className="space-y-3">
              {[
                { href: "/termos-de-uso", label: "Termos de Uso" },
                { href: "/politica-privacidade", label: "Política de Privacidade" },
                { href: "/aviso-isencao", label: "Aviso de Isenção" },
                { href: "/dpia", label: "DPIA" },
                { href: "/dpa", label: "DPA" },
                { href: "/plano-incidentes", label: "Plano de Incidentes" }
              ].map((link) => (
                <li key={link.href}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-lia-green transition-colors duration-200"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contato */}
          <div>
            <h3 className="text-lg font-bold mb-6 text-white">Contato</h3>
            <div className="space-y-4">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-3 text-gray-400 hover:text-lia-green transition-colors duration-200"
              >
                <div className="w-8 h-8 bg-gray-800 rounded-lg flex items-center justify-center">
                  <Mail className="w-4 h-4" />
                </div>
                <span><EMAIL></span>
              </a>

              <a
                href="https://wa.me/5517998761086"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 text-gray-400 hover:text-lia-green transition-colors duration-200"
              >
                <div className="w-8 h-8 bg-gray-800 rounded-lg flex items-center justify-center">
                  <Phone className="w-4 h-4" />
                </div>
                <span>+55 17 99876-1086</span>
              </a>

              <div className="flex items-center gap-3 text-gray-400">
                <div className="w-8 h-8 bg-gray-800 rounded-lg flex items-center justify-center">
                  <MapPin className="w-4 h-4" />
                </div>
                <span>São José do Rio Preto - SP</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-gray-400 text-sm">
            &copy; {new Date().getFullYear()} Lia IA. Todos os direitos reservados.
          </p>
          <div className="flex flex-col md:flex-row items-center gap-4">
            <span className="text-gray-400 text-sm">
              Desenvolvido por Matheus Rodrigo da Silva Santos
            </span>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-lia-green rounded-full"></div>
              <span className="text-gray-400 text-sm">Sistema Online</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
);

export default Footer;
