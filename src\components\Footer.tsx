
import { MessageCircle, Mail, MapPin, Phone } from "lucide-react";

const Footer = () => (
  <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Main Footer */}
      <div className="py-20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Brand */}
          <div className="space-y-8">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-lia-green to-emerald-500 rounded-2xl flex items-center justify-center p-2 shadow-xl">
                <img
                  src="/lia-logo.png"
                  alt="Lia Logo"
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="text-3xl font-black">LIA</span>
            </div>
            <p className="text-gray-300 leading-relaxed text-lg">
              Sua assistente financeira inteligente que transforma a forma como você controla seu dinheiro.
              <span className="text-lia-green font-semibold"> Simples, seguro e eficiente.</span>
            </p>
            <div className="flex items-center gap-3 px-4 py-2 bg-lia-green/10 rounded-xl border border-lia-green/20">
              <div className="w-3 h-3 bg-lia-green rounded-full animate-pulse"></div>
              <span className="text-sm text-lia-green font-semibold">Online 24/7</span>
            </div>
          </div>

          {/* Links Rápidos */}
          <div>
            <h3 className="text-xl font-black mb-8 text-white">Links Rápidos</h3>
            <ul className="space-y-4">
              {[
                { href: "#como-funciona", label: "Como Funciona" },
                { href: "#vantagens", label: "Vantagens" },
                { href: "#planos", label: "Planos" },
                { href: "#depoimentos", label: "Depoimentos" },
                { href: "#faq", label: "FAQ" }
              ].map((link) => (
                <li key={link.href}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-lia-green transition-all duration-300 block py-2 px-3 rounded-lg hover:bg-lia-green/10 font-medium"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Documentos Legais */}
          <div>
            <h3 className="text-xl font-black mb-8 text-white">Documentos</h3>
            <ul className="space-y-4">
              {[
                { href: "/termos-de-uso", label: "Termos de Uso" },
                { href: "/politica-privacidade", label: "Política de Privacidade" },
                { href: "/aviso-isencao", label: "Aviso de Isenção" },
                { href: "/dpia", label: "DPIA" },
                { href: "/dpa", label: "DPA" },
                { href: "/plano-incidentes", label: "Plano de Incidentes" }
              ].map((link) => (
                <li key={link.href}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-lia-green transition-all duration-300 block py-2 px-3 rounded-lg hover:bg-lia-green/10 font-medium"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contato */}
          <div>
            <h3 className="text-xl font-black mb-8 text-white">Contato</h3>
            <div className="space-y-6">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-4 text-gray-300 hover:text-lia-green transition-all duration-300 group p-3 rounded-xl hover:bg-lia-green/10"
              >
                <div className="w-10 h-10 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl flex items-center justify-center group-hover:from-lia-green group-hover:to-emerald-500 transition-all duration-300 shadow-lg">
                  <Mail className="w-5 h-5" />
                </div>
                <span className="font-medium"><EMAIL></span>
              </a>

              <a
                href="https://wa.me/5517998761086"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-4 text-gray-300 hover:text-lia-green transition-all duration-300 group p-3 rounded-xl hover:bg-lia-green/10"
              >
                <div className="w-10 h-10 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl flex items-center justify-center group-hover:from-lia-green group-hover:to-emerald-500 transition-all duration-300 shadow-lg">
                  <Phone className="w-5 h-5" />
                </div>
                <span className="font-medium">+55 17 99876-1086</span>
              </a>

              <div className="flex items-center gap-4 text-gray-300 p-3 rounded-xl">
                <div className="w-10 h-10 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl flex items-center justify-center shadow-lg">
                  <MapPin className="w-5 h-5" />
                </div>
                <span className="font-medium">São José do Rio Preto - SP</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-700/50 py-10">
        <div className="flex flex-col md:flex-row justify-between items-center gap-6">
          <p className="text-gray-300 font-medium">
            &copy; {new Date().getFullYear()} Lia IA. Todos os direitos reservados.
          </p>
          <div className="flex flex-col md:flex-row items-center gap-6">
            <span className="text-gray-300 font-medium">
              Desenvolvido por Matheus Rodrigo da Silva Santos
            </span>
            <div className="flex items-center gap-3 px-4 py-2 bg-lia-green/10 rounded-xl border border-lia-green/20">
              <div className="w-3 h-3 bg-lia-green rounded-full animate-pulse"></div>
              <span className="text-lia-green font-semibold text-sm">Sistema Online</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
);

export default Footer;
