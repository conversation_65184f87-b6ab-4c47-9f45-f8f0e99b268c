
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const PoliticaPrivacidade = () => (
  <div className="min-h-screen bg-white">
    <Header />
    <main className="pt-28 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="prose prose-lg max-w-none">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-black text-gray-900 mb-4">📄 POLÍTICA DE PRIVACIDADE</h1>
            <h2 className="text-2xl font-bold text-lia-green mb-2">LIA – ASSISTENTE FINANCEIRA</h2>
          </div>

          <div className="bg-gradient-to-r from-lia-green/10 to-emerald-500/10 p-6 rounded-2xl mb-8 border border-lia-green/20">
            <p className="text-sm text-gray-700 mb-2">
              <strong>Última atualização:</strong> {new Date().toLocaleDateString('pt-BR')}
            </p>
            <p className="text-sm text-gray-700">
              <strong>Versão:</strong> 1.0
            </p>
          </div>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">1. DADOS COLETADOS</h2>
            <div className="space-y-3">
              <p className="text-gray-700 leading-relaxed">
                <strong>I –</strong> Dados cadastrais: nome, telefone e informações fornecidas pelo usuário;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>II –</strong> Dados financeiros: receitas, despesas, saldos e metas;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>III –</strong> Conteúdos multimídia: mensagens de texto, imagens e áudios enviados;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>IV –</strong> Dados de uso, registros de interações e logs.
              </p>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">2. FINALIDADE DO TRATAMENTO</h2>
            <div className="space-y-3">
              <p className="text-gray-700 leading-relaxed">
                <strong>I –</strong> Prestação dos serviços de controle financeiro pessoal e elaboração de relatórios;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>II –</strong> Melhoria contínua da experiência e segurança da ferramenta;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>III –</strong> Cumprimento de obrigações legais ou regulatórias, quando aplicável.
              </p>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">3. ARMAZENAMENTO E SEGURANÇA</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Os dados são armazenados no serviço Supabase, com aplicação de práticas de segurança compatíveis com padrões técnicos e administrativos exigidos pela legislação vigente. Destaca-se que, mesmo com tais medidas, não existe sistema inviolável na internet.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">4. COMPARTILHAMENTO DE DADOS</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Os dados não serão compartilhados com terceiros, salvo:
            </p>
            <div className="space-y-3">
              <p className="text-gray-700 leading-relaxed">
                <strong>I –</strong> Com consentimento expresso do usuário;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>II –</strong> Por determinação legal ou ordem judicial.
              </p>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">5. PRAZO DE RETENÇÃO</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Os dados permanecerão armazenados enquanto o usuário mantiver a conta ativa. Em caso de solicitação de exclusão, os dados serão eliminados no prazo de até 30 (trinta) dias.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">6. DIREITOS DO TITULAR</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              O usuário poderá solicitar, a qualquer tempo:
            </p>
            <div className="space-y-3">
              <p className="text-gray-700 leading-relaxed">
                <strong>I –</strong> Acesso aos dados;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>II –</strong> Correção de informações inexatas;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>III –</strong> Revogação de consentimento;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>IV –</strong> Exclusão definitiva de seus dados.
              </p>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">7. CANAL DE CONTATO</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Para exercer seus direitos, o usuário poderá entrar em contato pelo e-mail <EMAIL> ou pelo WhatsApp +55 17 3198-1086.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">8. LOCALIDADE</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              A operação é sediada na cidade de São José do Rio Preto – SP.
            </p>
          </section>

          <div className="bg-gray-50 p-6 rounded-2xl mt-12">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Contato</h3>
            <div className="space-y-2 text-gray-700">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>WhatsApp:</strong> +55 17 3198-1086</p>
              <p><strong>Localidade:</strong> São José do Rio Preto – SP</p>
            </div>
          </div>
        </div>
      </div>
    </main>
    <Footer />
  </div>
);

export default PoliticaPrivacidade;
