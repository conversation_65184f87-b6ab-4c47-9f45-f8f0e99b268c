
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const AvisoIsencao = () => (
  <div className="min-h-screen bg-white">
    <Header />
    <main className="pt-28 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="prose prose-lg max-w-none">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-black text-gray-900 mb-4">📄 AVISO DE ISENÇÃO DE RESPONSABILIDADE E RISCOS</h1>
            <h2 className="text-2xl font-bold text-lia-green mb-2">LIA – ASSISTENTE FINANCEIRA</h2>
          </div>

          <div className="bg-gradient-to-r from-lia-green/10 to-emerald-500/10 p-6 rounded-2xl mb-8 border border-lia-green/20">
            <p className="text-sm text-gray-700 mb-2">
              <strong>Última atualização:</strong> {new Date().toLocaleDateString('pt-BR')}
            </p>
            <p className="text-sm text-gray-700">
              <strong>Versão:</strong> 1.0
            </p>
          </div>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">1. RISCOS INERENTES AO USO</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              O usuário reconhece que, embora adotadas medidas técnicas e administrativas para a proteção de dados pessoais e sensíveis, existem riscos inerentes ao ambiente digital, incluindo falhas técnicas, incidentes de segurança e acessos não autorizados.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">2. LIMITAÇÃO DE RESPONSABILIDADE</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              O responsável pela ferramenta não se responsabiliza:
            </p>
            <div className="space-y-3">
              <p className="text-gray-700 leading-relaxed">
                <strong>I –</strong> Por decisões financeiras, perdas, danos ou prejuízos de qualquer natureza resultantes da utilização das informações disponibilizadas pela ferramenta;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>II –</strong> Por incidentes de segurança ou falhas operacionais alheias ao controle direto e exclusivo do controlador;
              </p>
              <p className="text-gray-700 leading-relaxed">
                <strong>III –</strong> Pelo conteúdo enviado pelo próprio usuário, de exclusiva responsabilidade deste.
              </p>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">3. CONSENTIMENTO E ISENÇÃO FORMAL</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              O usuário consente expressamente com o tratamento de seus dados, inclusive dados sensíveis e conteúdos multimídia, nos termos estabelecidos, e declara ciência dos riscos inerentes à utilização do serviço, isentando o desenvolvedor identificado no Termo de Uso de responsabilidade por fatos não atribuíveis à sua culpa exclusiva.
            </p>
          </section>

          <div className="bg-gray-50 p-6 rounded-2xl mt-12">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Contato</h3>
            <div className="space-y-2 text-gray-700">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>WhatsApp:</strong> +55 17 3198-1086</p>
              <p><strong>Localidade:</strong> São José do Rio Preto – SP</p>
            </div>
          </div>
        </div>
      </div>
    </main>
    <Footer />
  </div>
);

export default AvisoIsencao;
