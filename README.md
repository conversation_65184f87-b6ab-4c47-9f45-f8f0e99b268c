# Lia Digital Oasis

## Sobre o Projeto

**Lia** é uma assistente financeira inteligente que funciona diretamente pelo WhatsApp, ajudando você a organizar sua vida financeira de forma simples, segura e eficiente.

## Como editar este código?

Existem várias maneiras de editar esta aplicação.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd lia-digital-oasis

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## Tecnologias Utilizadas

Este projeto foi construído com:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Lucide React (ícones)

## Funcionalidades

- ✅ Interface responsiva e moderna
- ✅ Seções informativas sobre a Lia
- ✅ Planos de assinatura
- ✅ FAQ interativo
- ✅ Depoimentos de usuários
- ✅ Integração com WhatsApp
- ✅ Design otimizado para conversão

## Estrutura do Projeto

- `/src/components` - Componentes React reutilizáveis
- `/src/pages` - Páginas da aplicação
- `/public` - Arquivos estáticos (logo, imagens)
- `/src/index.css` - Estilos globais e configurações do Tailwind
