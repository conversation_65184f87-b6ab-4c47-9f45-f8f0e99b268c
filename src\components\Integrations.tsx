
import { Smartphone, Shield, Zap, Globe } from "lucide-react";

const integrations = [
  {
    icon: Smartphone,
    label: "WhatsApp",
    desc: "Toda a experiência direta no seu WhatsApp, sem complicação.",
    color: "from-green-500 to-emerald-600"
  },
  {
    icon: Shield,
    label: "Segurança Avançada",
    desc: "Criptografia de ponta a ponta e proteção total dos seus dados.",
    color: "from-blue-500 to-blue-600"
  },
  {
    icon: Zap,
    label: "IA Inteligente",
    desc: "Processamento instantâneo com tecnologia de última geração.",
    color: "from-purple-500 to-purple-600"
  },
  {
    icon: Globe,
    label: "Cloud Seguro",
    desc: "Seus dados sempre disponíveis e protegidos na nuvem.",
    color: "from-orange-500 to-red-500"
  }
];

const Integrations = () => (
  <section className="py-24 bg-white" id="integracoes">
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="text-center max-w-3xl mx-auto mb-16">
        <div className="inline-flex items-center gap-2 px-4 py-2 bg-lia-green/10 rounded-full mb-6">
          <Globe className="w-4 h-4 text-lia-green" />
          <span className="text-sm font-semibold text-lia-green">TECNOLOGIA</span>
        </div>
        <h2 className="text-4xl sm:text-5xl font-black text-gray-900 mb-6">
          Tecnologia de
          <span className="block bg-gradient-to-r from-lia-green to-emerald-500 bg-clip-text text-transparent">
            última geração
          </span>
        </h2>
        <p className="text-xl text-gray-600 leading-relaxed">
          Desenvolvida com as melhores ferramentas para sua segurança e praticidade
        </p>
      </div>

      {/* Integrations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {integrations.map(({ icon: Icon, label, desc, color }, idx) => (
          <div key={idx} className="group">
            <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 hover:border-lia-green/20 h-full text-center">
              {/* Icon */}
              <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br ${color} text-white rounded-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                <Icon className="w-8 h-8" />
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-lia-green transition-colors duration-300">
                {label}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {desc}
              </p>

              {/* Hover Effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-lia-green to-emerald-500 opacity-0 group-hover:opacity-5 transition-opacity duration-300"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Trust Section */}
      <div className="mt-20">
        <div className="bg-gradient-to-br from-gray-50 to-emerald-50/30 rounded-3xl p-8 lg:p-12">
          <div className="text-center mb-12">
            <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
              Confie na tecnologia que protege milhões
            </h3>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Utilizamos os mesmos padrões de segurança dos maiores bancos do mundo
            </p>
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            {[
              { icon: Shield, label: "Criptografia SSL", desc: "Dados protegidos" },
              { icon: Zap, label: "99.9% Uptime", desc: "Sempre disponível" },
              { icon: Globe, label: "Backup Automático", desc: "Nunca perca dados" },
              { icon: Smartphone, label: "LGPD Compliance", desc: "Totalmente regulamentado" }
            ].map(({ icon: Icon, label, desc }, idx) => (
              <div key={idx} className="space-y-3">
                <div className="w-12 h-12 bg-gradient-to-br from-lia-green to-emerald-500 rounded-xl mx-auto flex items-center justify-center">
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="font-bold text-gray-900">{label}</div>
                  <div className="text-sm text-gray-600">{desc}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default Integrations;
