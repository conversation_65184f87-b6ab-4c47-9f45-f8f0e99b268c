
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const PlanoIncidentes = () => (
  <div className="min-h-screen bg-white">
    <Header />
    <main className="pt-28 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="prose prose-lg max-w-none">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-black text-gray-900 mb-4">📄 PLANO DE RESPOSTA A INCIDENTES DE DADOS</h1>
            <h2 className="text-2xl font-bold text-lia-green mb-2">LIA – ASSISTENTE FINANCEIRA</h2>
            <p className="text-lg text-gray-600">Objetivo: Definir ações para resposta imediata e mitigação de incidentes.</p>
          </div>

          <div className="bg-gradient-to-r from-lia-green/10 to-emerald-500/10 p-6 rounded-2xl mb-8 border border-lia-green/20">
            <p className="text-sm text-gray-700 mb-2">
              <strong>Última atualização:</strong> {new Date().toLocaleDateString('pt-BR')}
            </p>
            <p className="text-sm text-gray-700">
              <strong>Versão:</strong> 1.0
            </p>
          </div>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Equipe</h2>
            <div className="space-y-2 text-gray-700">
              <p><strong>Controlador:</strong> Matheus Rodrigo da Silva Santos</p>
              <p><strong>Operadores:</strong> OpenAI, Supabase, Z-API, n8n</p>
            </div>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Classificação de Incidentes</h2>
            <ul className="list-disc pl-6 space-y-2 text-gray-700">
              <li><strong>Nível 1:</strong> Falha pontual</li>
              <li><strong>Nível 2:</strong> Vazamento controlado</li>
              <li><strong>Nível 3:</strong> Vazamento massivo</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Ações</h2>
            <ol className="list-decimal pl-6 space-y-2 text-gray-700">
              <li>Conter e isolar o incidente</li>
              <li>Comunicar os envolvidos</li>
              <li>Registrar logs e evidências</li>
              <li>Notificar ANPD (nível 2 ou 3) em até 2 dias úteis</li>
              <li>Informar usuários afetados via e-mail e/ou WhatsApp</li>
            </ol>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Registro</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Manter histórico de incidentes.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Revisão</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Analisar causas e revisar controles após cada incidente.
            </p>
          </section>

          <div className="bg-gray-50 p-6 rounded-2xl mt-12">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Contato</h3>
            <div className="space-y-2 text-gray-700">
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>WhatsApp:</strong> +55 17 3198-1086</p>
              <p><strong>Localidade:</strong> São José do Rio Preto – SP</p>
            </div>
          </div>
        </div>
      </div>
    </main>
    <Footer />
  </div>
);

export default PlanoIncidentes;
